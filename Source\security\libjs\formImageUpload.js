/**
 * Executando metodo selectImage de contentComomnsDetais
 */
function selectImage(imageId, imageName, imagePath, componente, imageAuthor, imageSubtitle, imageDescription, tipoImagem, imagePathMini, tituloImagem, altImagem){
	imgId = document.getElementById("imgId").value;
	//SAVE
	if (imgId == "0")
	{
		window.opener.selectImage(imageId, imageName, imagePath, componente, imageAuthor, imageSubtitle,imageDescription, tipoImagem, imagePathMini, tituloImagem, altImagem);
		window.close();
	}
	else//UPDATE
	{
		window.close();
	}
}

/**
 * Valida��o dos campos da tela para formImageUpload.jsp
 */
function validateImageFields()
{
	var formok = 1;
	var mensagem = "";
	var msg = "";
	
	/*if(document.getElementById("image").value == "")
	{ 	
		msg = msg + "<strong>Imagem � um campo obrigat�rio.</strong><br>";
		formok = 0;
	}*/
	
	if($('#autor_li').attr('class') == "off"){
	
		if($('#descricao').val() == "") 
		{
			msg = msg + "<strong>Descri��o � um campo obrigat�rio.</strong><br>";
			formok = 0;
		}
	}else{
		
		if($('#autor').val() == "") 
		{
			msg = msg + "<strong>Autor � um campo obrigat�rio.</strong><br>";
			formok = 0;
		}
		
		if($('#legenda').val() == "") 	
		{
			msg = msg + "<strong>Legenda � um campo obrigat�rio.</strong><br>";
			formok = 0;
		}
	}
	
	mensagem = mensagem + msg;
	
	if(!formok) {
		document.getElementById("mensagemerro").innerHTML = mensagem;
		document.getElementById("mensagemerro").className = "on";
		return false;
	}
	
	return true;
}

function enviarImagemUpload() {
	
	var retorno = validateImageFields();
	
	if(retorno){
		imgId = document.getElementById("imgId").value;
		if (imgId == "0")//cadastro
		{
			site = document.getElementById("site").value;
			componente = document.getElementById("componente").value;
			
			if($('#autor_li').attr('class') == 'off'){
				tipoImagem = true;
			}else{
				tipoImagem = false;
			}
			
			document.forms[0].action = "imageUpload.jsp?action=SAVE&site="+site+"&componente="+componente+"&tipoImagem="+tipoImagem;
			document.forms[0].submit();
		}
		else//altera��o
		{
			document.forms[0].action = "imageUpload.jsp?action=UPDATE";
			document.forms[0].submit();
		}
		document.getElementById("loading").className = "on";
		document.getElementById("salvar").className = "off";
		
	}
	else{
		return false;
	}
}

function editarImagem(){
	
	site = document.getElementById("site").value;
	componente = document.getElementById("componente").value;
	//tipoImagem = document.getElementById("tipoImagem").checked;

	document.forms[0].action = "imageUpload.jsp?action=CROPIMAGE&site="+site+"&componente="+componente;
	document.forms[0].submit();
}

function novaImagem(){
	site = document.getElementById("site").value;
	componente = document.getElementById("componente").value;
	document.forms[0].action = "imageUpload.jsp?action=NEWOBJECT&site="+site+"&componente="+componente;
	document.forms[0].submit();
}
