handlers= java.util.logging.FileHandler

# arquivo de log
java.util.logging.FileHandler.level=INFO
java.util.logging.FileHandler.pattern=c:/temp/webp_pss.log
java.util.logging.FileHandler.limit=50000
java.util.logging.FileHandler.count=1
java.util.logging.FileHandler.formatter=java.util.logging.SimpleFormatter

# log no console
java.util.logging.ConsoleHandler.level=INFO
java.util.logging.ConsoleHandler.formatter=java.util.logging.SimpleFormatter

#-----------------------------------------------------#
# [INTRANET]
#-----------------------------------------------------#

INTRANET.level=INFO
INTRANET.handlers=java.util.logging.FileHandler


#-----------------------------------------------------#
# [PSS]
#-----------------------------------------------------#

PSS.level=INFO
PSS.DB.level=INFO
PSS.TX.level=INFO
PSS.NET.level=INFO
PSS.handlers=java.util.logging.FileHandler
log4j.appender.CONSOLE.layout.ConversionPattern=[%p] %d{dd/MM/yy HH:mm:ss} - %m%n

#-----------------------------------------------------#
# [Caronte]
#-----------------------------------------------------#

ACSS.level=INFO
ACSS.handlers=java.util.logging.FileHandler
# log4j.appender.CONSOLE.layout.ConversionPattern = [%p] %d{dd/MM/yy HH:mm:ss} - %m%n