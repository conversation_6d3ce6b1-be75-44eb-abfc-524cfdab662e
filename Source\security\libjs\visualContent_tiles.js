var bufferErr='';

function setError(msg) {
	bufferErr += msg + "\n"
}


function submet(form, action)
{
	if(action == 'SAVE')
	{
		
		if(form.approved != null)
		{
		
			if(form.approved.checked == true)
			{			
				if(rTrim(form.justification.value).length == 0)
				{
					setError("Ao reprovar um conteúdo o campo justificativa é obrigatório");
				}		
			}
		}
		
	}

	if(bufferErr.length == 0)
	{	
		/**
		* sitePid
		*/
		var sitePidParam = '';
		if(form.site.value != '')
		{
			sitePidParam = '&site='+form.site.value;							
		}
		
		
		/**
		* contentPid
		*/
		var contentPidParam = '';
		if(form.pid.value != '')
		{
			contentPidParam = '&pid='+form.pid.value;							
		}
		
	
		/**
		* componentPid
		*/
		var componentPidParam = '';
		if(form.componentPid.value != '')
		{
			componentPidParam = '&componentPid='+form.componentPid.value;							
		}

		form.action = jspFile + "?action=" + action + sitePidParam + contentPidParam + componentPidParam;

		form.submit();
	}
	else
	{
		alert(bufferErr);
		bufferErr = '';
		apagaMsg('');
	}
}

function newObject(form) {
	if(confirm("Os dados alterados ser�o perdidos.")) {
		form.pid.value = "";
		submet(form, 'SEARCH');
	}
}

function remove(form) {
	if(form.pid.value != "0" && form.pid.value != "") {
		if(confirm("Deseja remover este conte�do?")) {
			submet(form, 'REMOVE');
		}
	} else {
		alert("Não há conteúdo para ser Excluído. \nPrimeiro execute uma consulta para selecionar\n o conteúdo desejado.");
	}
}

function newSearch(form) {
	submet(form, 'NEWSEARCH');
}

function execSearch(form)
{

	bufferErr = "";
	doSearch(form);
	submet(form, 'SEARCH');	
}

function showHistory(form)
{
	if(form.pid.value == "0" || form.pid.value == "")
	{		
		alert("Selecione um conteúdo para visualizar seu histórico através da pesquisa de conte�dos.");
		return;
	}
	submet(form, 'SHOWCONTENTHISTORY');
}

function viewCommentaries(form)
{
	submet(form, 'VIEWCOMMENTARIES');
}

function apenasNumeros(string) 
{
    var numsStr = string.replace(/[^0-9]/g,'');
    return parseInt(numsStr);
}

function newGroup(form)
{
	if (form != null && form.action != null && form.site != null && form.site.value != null) 
	{
		var sitePid = apenasNumeros(form.site.value);
		form.action = "groupName_tiles.jsp?action=newobject&site="+sitePid+"&componentPid="+form.componentPid.value+"&jspFile="+form.jspFile.value;
	} else {
		form.action = "groupName_tiles.jsp?action=newobject&site="+form.site.value;
	}
	form.submit();
}


/*----------------------------------------------------------------*\
Fun��o: void doSave
Parametros: form
Descri��o: fun��o de apoio para save()
\*----------------------------------------------------------------*/
function doSave(form) {
	var editionDate = form.editionDate.value;
	var removeDate  = form.removeDate.value;
	var dateError = false;

	if(!validateDate(editionDate, "Data de Edição", true))
		dateError = true;

	if(!validateDate(removeDate, "Data de Remoção", false))
		dateError = true;

	if(!dateError && removeDate.length != 0)
		compareDates(editionDate, "Data de Edição", removeDate, "Data de Remoção", true);
}

function enableDisableJustificationField(toPublish)
{
	var labelButton = '';	
	var oper;	
	if(document.getElementById("approved").checked == true)
	{
		labelButton = "Reprovar";	
		oper = true;
	}
	else
	{
		if(toPublish)
		{
			labelButton = "Publicar";
		}
		else
		{
			labelButton = "Enviar";
		}
		oper = false;
		
	}		
	
	if(oper){
		
		document.getElementById('trApproved').style.display = '';
	}
	else{
		document.getElementById('trApproved').style.display = 'none';
	}	
	
	document.getElementById('btnSalvar').value = labelButton;
}


var http = getHTTPObject(); // We create the HTTP Object
       	
var sitePidResponse;

function selectSiteNode(sitePid, nodeDescription, isLoad, fullPathNode, type, status, isAllComponents, isSuperUser,isCascade, toUseAjax, type)
{
	sitePidResponse = sitePid;
	if(sitePid == '')
	{
		return;
	}
	
	var _sitePid = 'site_'+sitePid;
	
	//Verifica se o elemento j� n�o existe na lista
	if(document.getElementById(_sitePid)!=null)
	{
		return;
	}	

	var newTR_GrantedSite = document.createElement('tr');
		newTR_GrantedSite.id 		= _sitePid;
	
	var newTD_GrantedSite 	= document.createElement('td');
	
	var newTABLE_GrantedSite = document.createElement('table');
		newTABLE_GrantedSite.id='tblGrantedSites_'+sitePid;		
		newTABLE_GrantedSite.border='1';
		newTABLE_GrantedSite.className ='input';	
		newTABLE_GrantedSite.width='100%'	
	var newTBODY_GrantedSite = document.createElement('tbody');	
	newTABLE_GrantedSite.appendChild(newTBODY_GrantedSite);				
	
	
	var newTR_GrantedSiteHeader = document.createElement("tr");
	
	var newTH_GrantedSiteName	= document.createElement("th");
		newTH_GrantedSiteName.width='70%';		
	newTH_GrantedSiteName.appendChild(document.createTextNode("SITE"));	
	
	var newTH_GrantedSiteRemove	= document.createElement("th");
	newTH_GrantedSiteRemove.width='10%';	
	newTH_GrantedSiteRemove.appendChild(document.createTextNode("REM"));
	
	
	var newTR_GrantedSiteValues2 = document.createElement("tr");						
	
	var newTD_GrantedSiteValues2	= document.createElement('td');
	newTD_GrantedSiteValues2.setAttribute('colSpan',4);
		       
   	newTR_GrantedSiteValues2.appendChild(newTD_GrantedSiteValues2);
		
	var newTR_GrantedSiteValues = document.createElement("tr");						

    var newTD_GrantedSiteValues_Nome 		= document.createElement('td');    
    newTD_GrantedSiteValues_Nome.appendChild(document.createTextNode(fullPathNode));        
    	
    var newHIDDEN_SitePid = document.createElement("input");
    	newHIDDEN_SitePid.setAttribute("name", "grantedSitesPid");    	
    	newHIDDEN_SitePid.setAttribute("value", sitePid);
      
    var newTD_GrantedSiteValues_Remove 		= document.createElement('td');
    var newHREF_Remove = document.createElement("a");	
		newHREF_Remove.href = "#duplicatedComponents";
		newHREF_Remove.innerHTML='<img src=image/btexcluir.gif border=0>';
		newHREF_Remove.onclick=function(){removeNode(_sitePid);} 
   	newTD_GrantedSiteValues_Remove.appendChild(newHREF_Remove);

	newTR_GrantedSiteHeader.appendChild(newTH_GrantedSiteName);
	newTR_GrantedSiteHeader.appendChild(newTH_GrantedSiteRemove);
	
	
	newTR_GrantedSiteValues.appendChild(newTD_GrantedSiteValues_Nome);	
	newTR_GrantedSiteValues.appendChild(newTD_GrantedSiteValues_Remove);
	
	newTABLE_GrantedSite.tBodies(0).appendChild(newTR_GrantedSiteHeader);							
	newTABLE_GrantedSite.tBodies(0).appendChild(newTR_GrantedSiteValues);
	newTABLE_GrantedSite.tBodies(0).appendChild(newTR_GrantedSiteValues2);	
												
	
	var tblGrantedSitesExternal = document.getElementById('tblGrantedSitesExternal');
	
	newTD_GrantedSite.appendChild(newTABLE_GrantedSite);
	newTR_GrantedSite.appendChild(newTD_GrantedSite);
	
	tblGrantedSitesExternal.tBodies(0).appendChild(newTR_GrantedSite);
	if(toUseAjax)
	{				      	      	      					
		refreshComponents(sitePid, type);	
	}
}

function removeNode(sitePid)
{
	var selectedContent = document.getElementById(sitePid);	 
	selectedContent.parentNode.removeChild(selectedContent);    
}

function refreshComponents(sitePid, type)
{
	element = sitePid;	
	//url = jspFile + '?action=GETCOMPONENTS&sitePid='+sitePid+'&type='+type;			
	url = '../tools/treeComponents.jsp?action=GETCOMPONENTS&sitePid='+sitePid+'&type='+type;	
	http.open("POST", url, true);
  	http.onreadystatechange = showResponse;
  	http.send(null);				
}

function getHTTPObject() {

  var xmlhttp;

  /*@cc_on

  @if (@_jscript_version >= 5)

    try {

      xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");

    } catch (e) {

      try {

        xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");

      } catch (E) {

        xmlhttp = false;

      }

    }

  @else

  xmlhttp = false;

  @end @*/

  if (!xmlhttp && typeof XMLHttpRequest != 'undefined') {

    try {

      xmlhttp = new XMLHttpRequest();

    } catch (e) {

      xmlhttp = false;

    }

  }

  return xmlhttp;

}

function showResponse()
{	
	if (http.readyState == 4) {
	
		var _form = document.forms[0];	
		var componentsText;
		if(http.responseText != null)
		{
		 	componentsText= http.responseText.split(";");
		}
		else
		{
			componentsText= http.split(";");
		}
		
		var newTR_GrantedComponent = document.createElement('tr');
		newTR_GrantedComponent.id = 'newTR_GrantedComponent_'+sitePidResponse;	
		
		var newTD_GrantedComponent 	= document.createElement('td');
			newTD_GrantedComponent.setAttribute('colSpan',4);
	
		
		var newTABLE_GrantedComponent = document.createElement('table');
			newTABLE_GrantedComponent.width='100%';
			newTABLE_GrantedComponent.className ='text';			
		newTABLE_GrantedComponent.border='0';
			
		var newTBODY_GrantedComponent = document.createElement('tbody');	
		newTABLE_GrantedComponent.appendChild(newTBODY_GrantedComponent);				
		
		
		var newTR_GrantedComponentHeader = document.createElement("tr");
		
		var urlExternal = document.getElementsByName('urlExternal');
		var selectedRadio;
		var disabled = ''
		var checked1 = 'checked';
		var checked2 = '';
		
		if(urlExternal != null && urlExternal.length > 0)
		{
			for(i=0;i<urlExternal.length;i++)
			{
				if(urlExternal[i].checked)
				{
					selectedRadio = urlExternal[i].value;
					break;
				}
			}
			if(selectedRadio != '1')
			{
				disabled = 'disabled';
				checked1 =  '';
				checked2 = 'checked';
			}
		}
		var newTH_GrantedComponent	= document.createElement('th');
			newTH_GrantedComponent.width='100%'
			newTH_GrantedComponent.innerHTML = 'Componentes Permitidos<br /><span style="display:none" id="spanOptions'+sitePidResponse+'">Gerar MORE deste conte�do?<br /> <input type="radio" name="gerarMore_'+sitePidResponse+'" '+disabled+' value="1" '+checked1+' /> SIM <input type="radio" name="gerarMore_'+sitePidResponse+'" value="0" '+checked2+' /> N�o</span>'
		
		newTR_GrantedComponentHeader.appendChild(newTH_GrantedComponent);
	    
	    newTABLE_GrantedComponent.tBodies(0).appendChild(newTR_GrantedComponentHeader);
		
		if(componentsText!="")
		{		
			for(i=0;i<componentsText.length;i++)
			{
				if(i % 2 == 0)
				{
					row = newTABLE_GrantedComponent.insertRow();
				}
			
				var component = new Array();
				if(componentsText[i].indexOf(",") == -1)
				{
					return;
				}
				
				component = componentsText[i].split(",");
				
				var componentPid = component[0];
				var componentName = component[1];			
				
				if(componentPid == '')
				{
					return;
				}								
				cell = row.insertCell();
				cell.innerHTML = '<input type="checkbox" id="component_sitePid'+sitePidResponse+'" name="recommendedComponentsPid" value="' + componentPid + '" onclick="javascript: showHiddenOption('+sitePidResponse+')">' + componentName;			
			}				
		}			
		newTD_GrantedComponent.appendChild(newTABLE_GrantedComponent);
		newTR_GrantedComponent.appendChild(newTD_GrantedComponent);
			
		var tblGrantedSites = document.getElementById('tblGrantedSites_'+sitePidResponse);	
		tblGrantedSites.tBodies(0).appendChild(newTR_GrantedComponent);	
		
	}
}

function mClk(src)
{ 
	if(event.srcElement.tagName == 'TD')
	{
		src.children.tags('A')[0].click();
	}
}
	
	
/*
	Function     : send()
	Description  : Sends TAG to textarea.
*/
function send(src)
{
	if(check_Nav())
	{
  		// Parent Object
   		var parent_objname = "body";  // parent editor objname
   		editor_insertHTML(parent_objname, src,"",0);
	}
	else // Netscape, Mozilla, Opera
	{
   		window.opener.document.eval('document.contentForm').value = window.opener.document.eval('document.contentForm').value + src;
	}
}

function processa()
{
	return "<img src="+this.image.value+" >";
}
	

/* ---------------------------------------------------------------------- *\
		Function      : check_Nav()
		Description   : Check browsers
		return        : True - IE
                False - Other
\* ---------------------------------------------------------------------- */
function check_Nav()
{
	// Check for IE 5.5+ on Windows
	var Agent, VInfo, MSIE, Ver, Win32, Opera;
	Agent = navigator.userAgent;
	VInfo = Array();                              // version info
	VInfo = Agent.split(";")
	MSIE  = Agent.indexOf('MSIE') > 0;
	Ver   = VInfo[1].substr(6,3);
	Win32 = Agent.indexOf('Windows') > 0 && Agent.indexOf('Mac') < 0 && Agent.indexOf('Windows CE') < 0;
	Opera = Agent.indexOf('Opera') > -1;
	//if (!MSIE || Opera || Ver < 5.5 || !Win32) { return; }
	return MSIE;
}

//fun��o para abrir nova janela para editar portfolio
function showCommentaries(sitePid, componentPid, pid)
{
	w1 = window.open('commentary.jsp?action=NEWSEARCH&site='+sitePid+'&componentPid='+componentPid+'&pid='+pid,'newwindow',
	'width=670, height=600, scrollbars=yes, resizable=no, localtion=no, directories=no, toolbar=no, titlebar=no, status=yes, menubar=no');
}

function showHiddenFieldsUrl(optionField)			
{
	var bodyFieldDivElem 	= document.getElementById("bodyFieldTR");
	//var linksListDivElem 	= document.getElementById("linksListTR");
	var urlOpenTypeTRElem	= document.getElementById("urlOpenTypeTR");
	var contentUrlTRElem 	= document.getElementById("contentUrlTR");
	var searchButton        = document.getElementById("contentSearchButton");
	var enableURL			= document.getElementById("enableURL");
	var urlContentPid		= document.getElementById('urlContentPid').value;
	var disableField		= document.getElementById('disableUrlField').value;
	//url externa
	if(optionField == '0')
	{		
		if(disableField == "")
		{
			document.contentForm.contentUrl.disabled = false;	
		}	
		
		if(novo)
		{			
			document.contentForm.contentUrl.value = "http://";
		}
		else
		{			
			//document.contentForm.contentUrl.value = 'http://';
			if(contentUrlOriginalJS == '')
			{			
				document.contentForm.contentUrl.value = 'http://';
			}
			else
			{						
				document.contentForm.contentUrl.value = contentUrlOriginalJS;
			}
		}							
						
		document.contentForm.contentUrl.style.background = '#ffffff';
		bodyFieldDivElem.style.display = "none";
		searchButton.style.display = "";
		//linksListDivElem.style.display = "none";
		document.contentForm.body.disabled = true;
		urlOpenTypeTRElem.style.display = "";
		contentUrlTRElem.style.display = "";
		
		if(novo)
		{
			document.contentForm.newWindow[0].checked= true;
			document.contentForm.newWindow[1].checked= false;
		}
			
		if(urlContentPid != '')
		{
			document.getElementById('urlExternalField').disabled = true;
			document.getElementById('enableURL').style.display = "";
		}		
	}
	//detalhes (more)
	else if(optionField == '1')
	{		
		if(urlContentPid != '')
		{
			alert('O valor da URL pesquisada anteriormente foi perdido. O valor da URL atual � o caminho do arquivo de detalhes do conte�do');
			document.getElementById('urlContentPid').value = '';
		}
		
		document.getElementById('enableURL').style.display = "none";
		document.contentForm.contentUrl.disabled = true;
		
		
		if( contentUrlOriginalJS == '')
		{
			document.contentForm.contentUrl.value = "Preenchimento Autom�tico"				
		}
		else
		{		
			document.contentForm.contentUrl.value = contentUrlOriginalJS;
		}			
				
		document.contentForm.contentUrl.style.background = '#D4D1D1';
		searchButton.style.display = 'none';						
		bodyFieldDivElem.style.display = "";
		//linksListDivElem.style.display = "";
		document.contentForm.body.disabled = false;
		urlOpenTypeTRElem.style.display = "";
		contentUrlTRElem.style.display = "";
		
		if(novo)
		{
			document.contentForm.newWindow[0].checked= false;
			document.contentForm.newWindow[1].checked= true;
		}
	}
	//nenhum
	else if(optionField == '2')
	{
		if(urlContentPid != '')
		{
			alert('O valor da URL pesquisada anteriormente foi perdido.');
			document.getElementById('urlContentPid').value = '';
		}
		document.contentForm.contentUrl.disabled = true;
		bodyFieldDivElem.style.display = "none";
		//linksListDivElem.style.display = "none";
		document.contentForm.body.disabled = true;
		urlOpenTypeTRElem.style.display = "none";
		contentUrlTRElem.style.display = "none";
	}	
	contentUrlOriginalJS = '';								
}

//Function utilizada para mostrar ou ocultar um determinado campo
function showHiddenDiv(id) {
	if (document.getElementById(id).style.display == 'none')
		document.getElementById(id).style.display = 'block';
	else
		document.getElementById(id).style.display = 'none';
}

//Function utilizada ao gerar a �rvore de sites em AJAX, concertando o BUG de ID's.
function swapFixer()
{
	/*---------------------------------------------------------------------*\
	|	Aqui s�o modificados os atributos ID de imagem, ID de span, e HREF, |
	|	todos relacionados da �rvore ASSUNTOS. Essa function � encarregada  |
	|	de corrigir o bug gerado ao criar a �rvore SITES via AJAX, pois ela |
	|	� gerada com um id id�ntico da �rvore assuntos, e nao pode ser      |
	|	alterado diretamente na sua taglib.                                 |
	\*---------------------------------------------------------------------*/
	var imgElements = document.getElementsByTagName('img');
	var anchorElements = document.getElementsByTagName('a');
	var spanElements = document.getElementsByTagName('span');
	
	var imgIndex = null;
	var anchorIndex = null;
	var spanIndex = null;
			
	for(index = 0;index<imgElements.length;index++)
	{
		if(imgElements[index].getAttribute('id') == 'itrtcntr1')
		{
			imgIndex = index;
			break;
		}
	}
	
	for(index = 0;index<anchorElements.length;index++)
	{
		if(anchorElements[index].getAttribute('href') == "javascript:swapFolder('itrtcntr1');")
		{
			anchorIndex = index;
			break;
		}
	}
	
	for(index = 0;index<spanElements.length;index++)
	{
		if(spanElements[index].getAttribute('id') == 'sitrtcntr1')
		{
			spanIndex = index;
			break;
		}
	}

	if(imgIndex != null)
	{
		helper(imgElements, imgIndex, 'itrtcntr');
	}
	
	if(spanIndex != null)
	{
		helper(spanElements, spanIndex, 'sitrtcntr');
	}

	if(anchorIndex != null)
	{
		for(anchorIndex;anchorIndex<anchorElements.length;anchorIndex++)
		{
			for(i=1, j=anchorIndex;j<anchorElements.length;i++, j++)
			{
				href="javascript:swapFolder('itrtcntr"+i+"');"
				if(href == anchorElements[j].getAttribute('href'))
				{
					tempHref = "javascript:swapFolder('itrtcntr"+i+"_');"
					anchorElements[j].setAttribute('href', tempHref);
				}
			}
		}
	}
}

//Function utilizada para auxiliar fun��o swapFixer() na altera��o de ID's duplicados.
function helper (array, index, id)
{
	for(index;index<array.length;index++)
	{
		for(i=1, j=index;j<array.length;i++, j++)
		{
			wrongId = id+i;
			currentId = array[j].getAttribute('id');			
			if(wrongId == currentId)
			{
				array[j].setAttribute('id', currentId+"_");
			}
		}
	}
}

function cleanHiddenFields()
{
	alert('O valor da URL pesquisada anteriormente foi perdido.');
	document.getElementById('urlExternalField').disabled = false;
	document.getElementById('urlExternalField').setAttribute('value', 'http://');
	document.getElementById('urlContentPid').setAttribute('value', '');
	document.getElementById('urlContentType').setAttribute('value', '');
	document.getElementById('enableURL').style.display = "none";
}

//Fun��o encarregada de exibir os radioButtons para controle de replica��o de conte�dos.
function showHiddenOption(siteID)
{
	var checked 		= new Array();
	var radioElements 	= document.getElementsByName('gerarMore_'+siteID);
	var checkElements 	= document.getElementsByName('recommendedComponentsPid')
	
	for(i = 0; i < checkElements.length; i++)
	{
		checkBox = checkElements[i];
		if(checkBox.id == 'component_sitePid'+siteID && checkBox.checked == true)
		{
			checked[checked.length] =  checkElements[i];
		}
		document.getElementById('spanOptions'+siteID).style.display = checked.length > 0? '':'none';
	}
}

/**
 * Gera o preview do conte�do selecionado.
 * @param form - Formul�rio principal.
 * @param link - Definido na Engine.
 */
var previewWindow = null;
function preview(form,link) 
{
	//var pid			= form.pid.value;
	var isPreview 	= form.isPreview.value;
	var	title		= form.title.value;
	
	if(isPreview == 'true')
	{
		if (title == "")   
		{		
			return;
		}
		previewWindow = window.open(link,'right=0,top=20,toolbar=yes,scrollbars=yes,resizable=yes');
		if (previewWindow.focus) {setTimeout('previewWindow.focus()', 1000);}
	}
}

function LayerContent(event, id)
{
	if (id == "gravataAjuda"){
		var obj = document.getElementById('gravataAjuda');
	}
	if (id == "chapeuAjuda"){
		var obj = document.getElementById('chapeuAjuda');
	}
	if (id == "grupoAjuda"){
		var obj = document.getElementById('grupoAjuda');
	}
	
	
	obj.style.left = event.x;
	obj.style.top = event.y;

	if (obj.style.visibility == 'visible')  {
		obj.style.visibility = 'hidden';
  	} else                {
  		obj.style.visibility = 'visible';
	}	
}

/**************************   NOVAS IMPLEMENTA��ES   **************************/
/**
 * Abre e fecha os boxes.
 * @return
 */
function openCloseBox(objName)
{
	var obj = document.getElementById(objName);
	
	if (obj.className.indexOf('off') == -1)
		obj.className = obj.className + ' off';
	else
		obj.className = obj.className.substring(0, obj.className.indexOf('off') - 1);
}

/**
 * ??????????????
 * @param link
 * @return
 */
function updateContent(link)
{
	document.forms[0].action = link;
	document.forms[0].submit();
}

/**
 * Escreve no bodyfield um elemento.
 * Exemplo: para importar imagens, este m�todo
 * � utilizado para escrever no cursor o conte�do:
 * <img src='...'/>
 * @param src
 * @return
 */
function write(src)
{
    //if(check_Nav()) {
    //    var parent_objname = "body";  // parent editor objname
     //   tinyMCE.execCommand("mceInsertContent", false, src);
    //} else {// Netscape, Mozilla, Opera
        tinyMCE.execCommand("mceInsertContent", false, src);
    //}
}

/**
 * M�todo callback utilizado pela janela de upload de arquivos.
 * @param fileId O id do arquivo selecionado
 * @param fileName O nome do arquivo selecionado 
 * @param filePath Caminho do arquivo em disco 
 */
function selectFile(fileId, fileName, filePath, componente)
{
	if (componente == "conteudo") 
	{
		
		
		// pega a instancia do edito bodyfield
		//var inst = tinyMCE.selectedInstance;
		//var htmlCode=inst.selection;
		// pega o texto selecionado
		//var selectedText = htmlCode.getContent({format : 'text'});
		// pega qual foi o n� selecionado ex. p, img, a ... etc
		//htmlCode.getNode().nodeName.toLowerCase();
		
		
		// NOVO EDITOR - 4.0
		var selectedText = tinymce.activeEditor.selection.getContent({format: 'text'});
		
		
		// se tiver texto selecionado, o nome do arquivo vira o texto selecionado
		
		if (selectedText != "") {
			src = '<a href="' + filePath + '" id="fileId[' + fileId + ']">' + selectedText + '</a>';
			write(src);
			return;
		}
		
		src = '<a href="' + filePath + '" id="fileId[' + fileId + ']">' + fileName + '</a>';
		write(src);
	}
	else if (componente == "frontFile") 
	{
		document.getElementById('frontFile').value = fileId;
		document.getElementById('frontFileName').innerHTML = '<a href="#showFile" onclick="window.open(\'' + filePath + '\', \'_blank\', \'toolbar=no,width=750,height=258\');">' + fileName + '</a><br/><a href="javascript:removeFrontFile(' + document.getElementsByName('site')[0].value + ', ' + document.getElementsByName('pid')[0].value + ', ' + fileId +');"><i class="fa fa-trash"></i>&nbsp;excluir</a>';
	}
	else if (componente == "arquivoAnexo")
	{
		if (document.getElementById('listaArquivoAnexo') != null) {
			document.getElementById('listaArquivoAnexo').innerHTML = "";
		}
		adicionarArquivoAnexo(fileId, fileName, filePath);
	}
}

/**
 * Metodo callback utilizado pela janela de conteudos relacionados.
 * @param contentId id do conteudo
 * @param contentUrl url do conteudo 
 * @param title titulo do conteudo 
 */
function selectTiedContent(contentId, contentUrl, title)
{
	// novo editor - TinyMCE 4 
	//var selectedText = tinymce.activeEditor.selection.getContent({format: 'text'});
	var selectedText = tinymce.activeEditor.selection.getContent();
	
	// se tiver texto selecionado, o nome do arquivo vira o texto selecionado
	if (selectedText != "") {
		src = '<a href="pid['+ contentId + ']">' + selectedText + '</a>';
		write(src);
		return;
	}
	
	src = '<a href="pid['+ contentId + ']">' + title + '</a>';
	write(src);
}

/**
 * M�todo callback utilizado pela janela de upload de flash.
 * @param fileId O id do arquivo selecionado
 * @param fileName O nome do arquivo selecionado 
 * @param filePath Caminho do arquivo em disco 
 */
function selectMidia(fileId, fileName, filePath)
{
	h = '<img src="' + tinyMCE.activeEditor.getParam('theme_href') + '/images/spacer.gif"' ;
	h += ' class="mceItemFlash"';

	width = 150;
	height = 50;
	
	h += ' title="' + 'src:\'' + filePath + '\',width:\''+ width + '\',height:\''+ height + '\',id:\'fileId[' + fileId + ']\'"';
	h += ' width="' + width + '"';
	h += ' height="' + height + '"';

	h += ' />';
	write(h);
}

/**
 * Remove uma imagem de capa selecionada para este conte�do
 * @return
 */
function removeFrontImage(site,vc,image)
{
	 RemoveFrontFileImage.removeImage(site,vc,image,{
			callback:function(){
				document.getElementById('frontImage').value = 0;
				document.getElementById('frontImageName').innerHTML = 'Nenhuma imagem foi selecionada.';
			},
			errorHandler: function (exception) {
				if(exception != null) {
					alert(exception);
				}
			}
	 }); 
}
 
 function removeImgRedeSocial(site,vc,image)
 {
 	 RemoveFrontFileImage.removeImage(site,vc,image,{
 			callback:function(){
 				document.getElementById('imgRedeSocialId').value = 0;
 				document.getElementById('imgRedeSocialName').innerHTML = 'Nenhuma imagem foi selecionada.';
 			},
 			errorHandler: function (exception) {
 				if(exception != null) {
 					alert(exception);
 				}
 			}
 	 }); 
 }

/**
 * Remove um arquivo de capa selecionada para este conte�do
 * @return
 */
function removeFrontFile(site,vc,file)
{
	 RemoveFrontFileImage.removeFile(site,vc,file,{
		callback:function(){
			document.getElementById('frontFile').value = 0;
			document.getElementById('frontFileName').innerHTML = 'Nenhum arquivo foi selecionado.';
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 	
}

function addImg(imageId, imageName, imagePath, imagePathMini, site, vc)
{
	html = '<li>';
	html = html + '<a href="#showImage" onclick="window.open(\'' + imagePath + '\', \'_blank\', \'toolbar=no,width=750,height=280\');"><div class="img-thumb" style="background-image:url('+imagePathMini+')" /></div></a><br/><a href="javascript:removeIdImage('+ site +','+ vc +','+ imageId +');"><i class="fa fa-trash"></i>&nbsp;excluir&nbsp;</a><a href="javascript:updateIdImage('+ imageId +');"><i class="fa fa-edit"></i>&nbsp;editar</a>';
	html = html + '</li>';
	
	document.getElementById("listImageName").innerHTML = document.getElementById("listImageName").innerHTML + html;
	addIdImage(imageId, imageName, imagePath, imagePathMini, site, vc);
}


function addImg2(imageId, imageName, imagePath, imagePathMini, site, vc)
{
	html = '<li class="media media-sm">';
	if(site != null && vc != null){
		html = html + '<a href="#showImage" class="media-left" onclick="window.open(\'' + imagePath + '\', \'_blank\', \'toolbar=no,width=750,height=280\');"><div class="img-thumb" style="background-image:url('+imagePathMini+')" /></div></a><br/><a href="javascript:removeIdImage('+ site +','+ vc +','+ imageId +');"><i class="fa fa-trash"></i>&nbsp;excluir&nbsp;</a><a href="javascript:updateIdImage('+ imageId +');"><i class="fa fa-edit"></i>&nbsp;editar</a>';
	}else{
		html = html + '<a href="#showImage" class="media-left" onclick="window.open(\'' + imagePath + '\', \'_blank\', \'toolbar=no,width=750,height=280\');"><div class="img-thumb" style="background-image:url('+imagePathMini+')" /></div></a><br/><a href="javascript:removeIdImage('+ imageId +');"><i class="fa fa-trash"></i>&nbsp;excluir&nbsp;</a><a href="javascript:updateIdImage('+ imageId +');"><i class="fa fa-edit"></i>&nbsp;editar</a>';
	}
	html = html + '</li>';
	
	document.getElementById("listImageName").innerHTML = document.getElementById("listImageName").innerHTML + html;
	addIdImage(imageId, imageName, imagePath, imagePathMini, site, vc);
}

	
function addIdImage(imageId, imageName, imagePath, imagePathMini)
{
	document.getElementById("idImages").value = document.getElementById("idImages").value + ";" + imageId;
	document.getElementById("nameImages").value = document.getElementById("nameImages").value + ";" + imageName; 
	document.getElementById("pathImages").value = document.getElementById("pathImages").value + ";" + imagePath; 
	document.getElementById("pathImagesMini").value = document.getElementById("pathImagesMini").value + ";" + imagePathMini;
}

function addUrlContent(link, pid) {
	document.getElementById("url").value = link;
	document.getElementById("urlContentPid").value = pid;
}

function removeIdImage(site,vc,imageId)
{
	
	ids = document.getElementById("idImages").value;
	names = document.getElementById("nameImages").value;
	paths = document.getElementById("pathImages").value;
	pathsMini = document.getElementById("pathImagesMini").value;
	textoIds = "";
	textoNames = "";
	textoPaths = "";
	textoPathsMini = "";
	
	html = "";
	arrayImages = ids.split(";");
	arrayNames = names.split(";");
	arrayPaths = paths.split(";");
	arrayPathsMini = paths.split(";");
		
	for (var i = 0; i <= arrayImages.length - 1; i++)
	{
		if (arrayImages[i] != "" && arrayImages[i] != imageId)
		{
			textoIds = textoIds + ";" + arrayImages[i];
			textoNames = textoNames + ";" + arrayNames[i];
			textoPaths = textoPaths + ";" + arrayPaths[i];
			textoPathsMini = textoPathsMini + ";" + arrayPathsMini[i];
			html = html + '<li>';
			html = html + '<a href="#showImage" onclick="window.open(\'' + arrayPaths[i] + '\', \'_blank\', \'toolbars=no,scrollbars=yes,width=750,height=600\');"><div class="img-thumb" style="background-image:url('+ arrayPathsMini[i] +')" /></div></a><br/><a href="javascript:removeIdImage('+ site +','+ vc +',' + arrayImages[i] +');"><i class="fa fa-trash"></i>&nbsp;excluir&nbsp;</a><a href="javascript:updateIdImage('+ arrayImages[i] +');"><i class="fa fa-edit"></i>&nbsp;editar</a>';
			html = html + '</li>';
		}
	}

	if (textoIds == "" && document.getElementById('listImage') != null) {
		document.getElementById('listImage').innerHTML = "Nenhuma imagem adicionada.";
	}
	document.getElementById("idImages").value = textoIds;
	document.getElementById("nameImages").value = textoNames;
	document.getElementById("pathImages").value = textoPaths;
	document.getElementById("pathImagesMini").value = textoPathsMini;
	document.getElementById("listImageName").innerHTML = html;
	
	// adicionado para remo��o das imagens excluidas do banco e fisicamente 
	removeFrontImageThumbs(site,vc,imageId);
	
}

/**
* Remove uma imagem de capa selecionada para este conte�do
* @return
*/
function removeFrontImageThumbs(site,vc,image)
{
	 RemoveFrontFileImage.removeImage(site,vc,image,{
			callback:function(){
			},
			errorHandler: function (exception) {
				if(exception != null) {
					alert(exception);
				}
			}
	 }); 
}

function updateIdImage(imageId)
{
	//alert('updateIdImage'+imageId);
	VerifyAccess.verifyUserSession({
		callback:function(){
			link = "imageUpload_tiles.jsp?action=CONSULT&imageId=" + imageId;
			window.open(link, 'Questões', 'toolbars=no,scrollbars=yes,width=750,height=600');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 	
}

function adicionarArquivoAnexo(fileId, fileName, filePath)
{
	html = '<p>';
	html = html + '<a href="#showFile" onclick="window.open(\'' + filePath + '\', \'_blank\', \'toolbars=no,scrollbars=yes,width=750,height=600\');">' + fileName + '</a><br/><a href="javascript:removerArquivoAnexo('+ fileId +');"><i class="fa fa-trash"></i>&nbsp;excluir&nbsp;</a>';
	html = html + '</p>';
	
	document.getElementById("listaNomesArquivosAnexos").innerHTML = document.getElementById("listaNomesArquivosAnexos").innerHTML + html;
	adicionarIdArquivoAnexo(fileId, fileName, filePath);
}
	
function adicionarIdArquivoAnexo(fileId, fileName, filePath)
{
	document.getElementById("idArquivosAnexos").value = document.getElementById("idArquivosAnexos").value + ";" + fileId;
	document.getElementById("nomeArquivosAnexos").value = document.getElementById("nomeArquivosAnexos").value + ";" + fileName; 
	document.getElementById("caminhoArquivosAnexos").value = document.getElementById("caminhoArquivosAnexos").value + ";" + filePath;   
}

function removerArquivoAnexo(fileId)
{
	ids = document.getElementById("idArquivosAnexos").value;
	names = document.getElementById("nomeArquivosAnexos").value;
	paths = document.getElementById("caminhoArquivosAnexos").value;
	textoIds = "";
	textoNames = "";
	textoPaths = "";
	html = "";
	arrayFiles = ids.split(";");
	arrayNames = names.split(";");
	arrayPaths = paths.split(";");
	for (var i = 0; i <= arrayFiles.length - 1; i++)
	{
		if (arrayFiles[i] != "" && arrayFiles[i] != fileId)
		{
			textoIds = textoIds + ";" + arrayFiles[i];
			textoNames = textoNames + ";" + arrayNames[i];
			textoPaths = textoPaths + ";" + arrayPaths[i];
			html = html + '<p>';
			html = html + '<a href="#showFile" onclick="window.open(\'' + arrayPaths[i] + '\', \'_blank\', \'toolbars=no,scrollbars=yes,width=750,height=600\');">' + arrayNames[i] + '</a><br/><a href="javascript:removerArquivoAnexo('+ arrayFiles[i] +');"><i class="fa fa-trash"></i>&nbsp;excluir&nbsp;</a>';
			html = html + '</p>';
		}
	}

	if (textoIds == "" && document.getElementById('listaArquivosAnexos') != null) {
		document.getElementById('listaArquivosAnexos').innerHTML = "Nenhum arquivo adicionado.";
	}
	document.getElementById("idArquivosAnexos").value = textoIds;
	document.getElementById("nomeArquivosAnexos").value = textoNames;
	document.getElementById("caminhoArquivosAnexos").value = textoPaths;
	document.getElementById("listaNomesArquivosAnexos").innerHTML = html;
	
	site = document.getElementsByName('site')[0].value
	contentPid = document.getElementsByName('pid')[0].value
	removeFileAnexo(site, contentPid, fileId);
}

function removeFileAnexo(site, contentPid, image) //ESTEFANIo
{
	 RemoveFrontFileImage.removeFile(site,contentPid,image,{
			callback:function(){
			},
			errorHandler: function (exception) {
				if(exception != null) {
					alert(exception);
				}
			}
	 }); 
}

function atualizarArquivoAnexo(fileId)
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			link = "fileUpload.jsp?action=CONSULT&fileId=" + fileId;
			window.open(link, 'Quest�es', 'toolbars=no,scrollbars=yes,width=750,height=600');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 	
}

/**
 * Abre um pop-up contendo os coment�rios de determinado conte�do.
 * @param {Object} _pid
 */
function openComentaries(_pid)
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			window.open('commentary2.jsp?pid='+_pid, '_blank', 'toolbars=no,scrollbars=yes,width=600,height=500');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
}

function openFileUpload()
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			window.open('fileUpload_tiles.jsp?action=NEWOBJECT&site=' + document.forms[0].site.value + '&componente=frontFile&wiki=' + (document.forms[0].wiki ? document.forms[0].wiki.value : 'false'), '_blank', 'toolbars=no,scrollbars=yes,width=600,height=460');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
}

function openModalFileUpload() 
{ 
  VerifyAccess.verifyUserSession({ 

    callback:function(){ 
      $('#modal-empty .modal-body').load( 
          'fileUpload_tiles.jsp?action=NEWOBJECT&site=' + document.forms[0].site.value + '&componente=frontFile&wiki=' + (document.forms[0].wiki ? document.forms[0].wiki.value : 'false'), 
          function (response, status, xhr) {          
                    if (status == "success") { 
                        $('#modal-empty').modal({ show: true }); 
                        $('#modal-empty .modal-header').html('<h3>Cadastro de Arquivo</h3>'); 
                    }           
          }); 
    }, 
    errorHandler: function (exception) { 
      if(exception != null) { 
        alert(exception); 
      } 
    } 
  });  
} 

function abrirArquivoAnexo()
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			window.open('fileUpload_tiles.jsp?action=NEWOBJECT&site=' + document.forms[0].site.value + '&componente=arquivoAnexo&wiki=' + (document.forms[0].wiki ? document.forms[0].wiki.value : 'false'), '_blank', 'toolbar=no,width=600,height=460');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
}

function openImageUploadRS()
{

	VerifyAccess.verifyUserSession({
		callback:function(){
			window.open('imageUpload_tiles.jsp?action=NEWOBJECT&site=' + document.forms[0].site.value + '&componente=imgRedeSocialId&componentPid='+ document.forms[0].componentPid.value +'&wiki=' + (document.forms[0].wiki ? document.forms[0].wiki.value : 'false'), '_blank', 'toolbar=no,width=1024,height=768');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
}

function openImageUpload()
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			window.open('imageUpload_tiles.jsp?action=NEWOBJECT&site=' + document.forms[0].site.value + '&componente=frontImage&componentPid='+ document.forms[0].componentPid.value +'&wiki=' + (document.forms[0].wiki ? document.forms[0].wiki.value : 'false'), '_blank', 'toolbar=no,width=1024,height=768');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
}

function openImageUploadThumbs()
{
	document.forms[0].uploadThumbs.click();
}

function uploadImageThumbs(link,ancora)
{	
	document.forms[0].action = link + ancora;
	document.forms[0].submit();
}

function openTiedContents()
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			open('tiedContents2_tiles.jsp?callFunction=ADDLINK&action=list&site='+document.forms[0].site.value+'&componentPid='+document.forms[0].componentPid.value+'&origin=relationed', 'pesquisa', 'width=930,height=768,toolbar=no,scrollbars=yes');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
}

function exportXMLContent()
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			open('downloadVisualContentXML.jsp?pid='+document.forms[0].pid.value, '_blank', 'toolbars=no,scrollbars=yes,width=830,height=550');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
}

function encurtarURL(link)
{
	document.forms[0].action = link;
	document.forms[0].submit();
}


function gerarXMLRevista(imagens)
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			open('downloadRevistaXML.jsp?xml="'+imagens+'"', '_blank', 'toolbars=no,scrollbars=yes,width=830,height=550');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	}); 
	
}
