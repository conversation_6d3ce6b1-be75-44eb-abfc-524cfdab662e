function executar(link, idBotao)
{
	document.getElementById("isPreview").value = "false";
	botao = document.getElementById(idBotao);
	if (botao != null) {
		document.getElementById(idBotao).disabled = true;
	}
	document.contentForm.action = link;
	document.contentForm.submit();
}

function executarPreview(link,enable)
{
	var editionDate = document.getElementById("editionDate").value;	
	
	if  (editionDate == null || editionDate == "")
	{
		/*$.jAlert("Data de publicação deve ser preenchida", "", function(){
		},{ title: 'Executar preview', draggable: false, closeOnEscape: false, show: 'slide', width:480 });
		return;*/
		$('#modal-conteudo-visualizar-data').modal('show');
	}
	if (enable == "true")
	{
		document.getElementById("isPreview").value = "true";
		//alert(document.getElementById("isPreview").value);
		//window.open(view, 'Preview', 'toolbars=no,scrollbars=yes,width=750,height=600');
		document.forms[0].action = link;
		document.forms[0].submit();		
	}
	else
	{
		/*$.jAlert("Este componente não exibe preview pois gera apenas conteúdo para a capa dos sites", "", function(){
		},{ title: 'Executar preview', draggable: false, closeOnEscape: false, show: 'slide', width:480 });*/
		$('#modal-conteudo-visualizar-componente').modal('show');
	}
}

function exibirHistorico(link) 
{
	//DWR - Valida a sessão do usuário. 
	VerifyAccess.verifyUserSession({
		callback:function(){
			//Se tudo estiver ok, abre a tela de histórico.
			openHistoryDWR(link);
		},
		errorHandler: function (exception) {
			if(exception != null) {
				$.jAlert(exception, "", function(){
				},{ title: 'Exibir histórico', draggable: false, closeOnEscape: false, show: 'slide', width:480 });
			}
		}
	});
}

function exibirHistoricoModal(link, modalTitle) 
{
	
	VerifyAccess.verifyUserSession({
		callback:function(){
			$('#modal-empty .modal-body').load(link, function (response, status, xhr) {
				
				if (status == "success") {
                    $('#modal-empty').modal({ show: true });
                    $('#modal-empty .modal-header h4.modal-title').html(modalTitle);
                }					
			})
		},
		errorHandler: function (exception) {
			if(exception != null) {
				alert(exception);
			}
		}
	});	
}

/**
* Função de retorno utilizada pelo DWR para abrir a pop-up histórico.
*/
function openHistoryDWR(link)
{
	form = document.forms[0];
	if(form.pid.value == "0" || form.pid.value == "")
	{		
		$.jAlert("Selecione um conteúdo para visualizar seu histórico através da pesquisa de conteúdos", "", function(){
		},{ title: 'Exibir histórico', draggable: false, closeOnEscape: false, show: 'slide', width:480 });
		return;
	}
	window.open(link, 'histórico', 'toolbars=no,scrollbars=yes,width=750,height=600');
}


function cancelarConteudo(link) {
//	form = document.forms[0];
//	if(form.pid.value != "0" && form.pid.value != "") {
//		if(r){
		   	executar(link);
//		}
//	} else{
		
//	}
}

function removerConteudo(link) {
	
	
	form = document.forms[0];
	if(form.pid.value != "0" && form.pid.value != "") {

		/*$.jConfirm('Deseja remover este conteúdo?', '', function(r) {
		    if(r){
		    	executar(link);
			}
		}, { title: 'Remover conteúdo', draggable: false, closeOnEscape: false, show: 'slide', width:480 }) ;*/
		
		
		$('.close_botoes').hide();
	    $('.show_loading').show();
	    
	    
		executar(link);
		

	} else {
		
		
		
		$.jAlert("Não há conteúdo para ser Excluído. Primeiro execute uma consulta para selecionar o conteúdo desejado", "", function(){
		},{ title: 'Remover conteúdo', draggable: false, closeOnEscape: false, show: 'slide', width:480 });
		
		
	}
	
	
	
}




function reprovarConteudo(linkPublicationTarget) {
	document.getElementById("approved").value = false;
	executar(linkPublicationTarget);
}

function openJustification()
{
	VerifyAccess.verifyUserSession({
		callback:function(){
			window.open('form/formJustification_tiles.jsp', 'Justificativa', 'toolbars=no,scrollbars=yes,width=700,height=230');
		},
		errorHandler: function (exception) {
			if(exception != null) {
				$.jAlert(exception, "", function(){
				},{ title: 'Exibir justificativa', draggable: false, closeOnEscape: false, show: 'slide', width:480 });
			}
		}
	});
}

function enviarLixeiraConteudo(link) {
	form = document.forms[0];
	if(form.pid.value != "0" && form.pid.value != "") {
		$('.close_botoes').hide();
	    $('.show_loading').show();
	    
		executar(link);
	} else {
		$.jAlert("Não há conteúdo para ser Enviado para Lixeira. Primeiro execute uma consulta para selecionar o conteúdo desejado", "", function(){
		},{ title: 'Enviar para lixeira conteúdo', draggable: false, closeOnEscape: false, show: 'slide', width:480 });
	}
}
